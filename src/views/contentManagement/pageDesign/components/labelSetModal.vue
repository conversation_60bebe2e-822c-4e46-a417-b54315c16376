<template>
  <BasicModal
    v-bind="$attrs"
    title="标签导航配置"
    width="900px"
    @ok="closeModal"
    @register="registerModal"
  >
    <div class="tip">
      <p>说明：</p>
      <p>01.PC与移动端共用一套配置</p>
      <p>02.PC用于C版左侧导航最底部</p>
      <p>03.移动端用于主页顶部TAB导航最右侧的按钮弹窗</p>
      <p>04.视频数为0的标签，PC和移动端默认不会展示</p>
    </div>
    <Tabs v-model:activeKey="activeKey">
      <TabPane key="1" :tab="`全部(${total})`" forceRender>
        <BasicTable @register="registerTable" />
      </TabPane>
      <TabPane key="2" :tab="`已添加(${addedTotal})`" forceRender>
        <BasicTable @register="registerAddedTable" :data-source="addedList">
          <template #action="{ record, index }">
            <TableAction :actions="setActions(record, index)" />
          </template>
        </BasicTable>
      </TabPane>
    </Tabs>
  </BasicModal>
</template>
<script lang="ts" setup name="LabelSetModal">
  import { h, ref, computed, onMounted } from 'vue';
  import { Tabs, Button, message } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';

  import { getTagList, TagProps } from '/@/api/videoManagement/tag';
  import {
    navigationAdd,
    navigationDel,
    navigationUp,
    navigationDown,
    navigationTop,
    navigationBottom,
  } from '/@/api/contentManagement/pageList';

  const TabPane = Tabs.TabPane;
  const activeKey = ref('1');
  const total = ref<string | number>('-');
  // const addedTotal = ref<string | number>('-');
  const addCheckedRows = ref<number[]>([]);
  const removeCheckedRows = ref<number[]>([]);
  const addedList = ref<TagProps[]>([]);
  const addedTotal = computed(() => addedList.value.length);
  let channel = '';
  const [registerModal, { closeModal, setModalProps }] = useModalInner((data) => {
    channel = data.channel;
  });

  const [registerTable, { reload, clearSelectedRowKeys, getSelectRows }] = useTable({
    fetchSetting: {
      pageField: 'page',
      sizeField: 'length',
    },
    beforeFetch(params) {
      // 处理升序降序
      if (params['field'] && params['order']) {
        params['orderType'] = params['field'] == 'videoCount' ? 1 : undefined;
        params['orderMode'] =
          params['field'] == 'videoCount'
            ? params['order'] == 'ascend'
              ? 1
              : undefined
            : undefined;
        delete params['field'];
        delete params['order'];
      }
    },
    api: async (params) => {
      const res = await getTagList(params);
      total.value = res?.data?.count || 0;
      return {
        items: res?.data?.list ?? [],
        total: res?.data?.count || 0,
      };
    },
    rowKey: (record) => {
      return record?.id;
    },
    columns: [
      {
        title: '标签ID',
        dataIndex: 'id',
      },
      {
        title: '标签名',
        dataIndex: 'tagName',
      },
      {
        title: '视频数',
        dataIndex: 'videoCount',
        sorter: true,
      },
    ],
    showIndexColumn: false,
    showTableSetting: false,
    bordered: true,
    pagination: {
      slotLeft: () =>
        h(
          Button,
          {
            type: 'primary',
            disabled: !addCheckedRows.value.length,
            onClick: async () => {
              const newItems = getSelectRows() as TagProps[];
              addedList.value.unshift(...newItems);
              setAddedTableData(addedList.value);
              // await navigationAdd({
              //   channel,
              //   tagIds: addCheckedRows.value,
              // });
              addCheckedRows.value = [];
              reload();
              // reloadAdded();
              clearSelectedRowKeys();
              message.success('添加成功');
            },
          },
          () => '添加' + addCheckedRows.value.length,
        ),
    },
    maxHeight: 500,
    rowSelection: {
      type: 'checkbox',
      onChange: (selectedRowKeys) => {
        addCheckedRows.value = selectedRowKeys as number[];
      },
      getCheckboxProps(record) {
        return {
          disabled: addedList.value.indexOf(record.id) > -1,
        };
      },
    },
    immediate: false,
  });
  const [
    registerAddedTable,
    {
      reload: reloadAdded,
      clearSelectedRowKeys: clearAddedSelectedRowKeys,
      setTableData: setAddedTableData,
    },
  ] = useTable({
    // fetchSetting: {
    //   pageField: 'page',
    //   sizeField: 'length',
    // },
    // beforeFetch(params) {
    //   params['isSetNavigation'] = 1;
    // },
    // api: async (params) => {
    //   const res = await getTagList(params);
    //   addedList.value = res?.data?.list || [];
    //   return {
    //     items: res?.data?.list ?? [],
    //     total: res?.data?.count || 0,
    //   };
    // },
    rowKey: (record) => {
      return record?.id;
    },
    columns: [
      {
        title: '标签ID',
        dataIndex: 'id',
      },
      {
        title: '标签名',
        dataIndex: 'tagName',
      },
      {
        title: '视频数',
        dataIndex: 'videoCount',
      },
    ],
    showIndexColumn: false,
    showTableSetting: false,
    bordered: true,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    pagination: {
      slotLeft: () =>
        h(
          Button,
          {
            type: 'primary',
            disabled: !removeCheckedRows.value.length,
            onClick: () => {
              navigationDel({
                channel,
                tagIds: removeCheckedRows.value,
              }).then(() => {
                reloadAdded();
                clearAddedSelectedRowKeys();
                message.success('移除成功');
              });
            },
          },
          () => '移除' + removeCheckedRows.value.length,
        ),
    },
    maxHeight: 500,
    rowSelection: {
      type: 'checkbox',
      onChange: (selectedRowKeys) => {
        removeCheckedRows.value = selectedRowKeys as number[];
      },
    },
    immediate: false,
  });
  function setActions(record, index) {
    return [
      {
        icon: 'material-symbols:vertical-align-top-rounded',
        tooltip: '置顶',
        ifShow: index !== 0,
        onClick: async () => {
          await navigationTop({ id: record.id });
          reloadAdded();
        },
      },
      {
        icon: 'iconoir:move-up',
        tooltip: '上移',
        ifShow: index !== 0,
        onClick: async () => {
          await navigationUp({ id: record.id });
          reloadAdded();
        },
      },
      {
        icon: 'iconoir:move-down',
        tooltip: '下移',
        ifShow: index !== Number(addedTotal.value) - 1,
        onClick: async () => {
          await navigationBottom({ id: record.id });
          reloadAdded();
        },
      },

      {
        icon: 'uil:trash-alt',
        color: 'error',
        tooltip: '删除',
        onClick: async () => {
          await navigationDel({ tagIds: [record.id], channel });
          reloadAdded();
        },
      },
    ];
  }
  onMounted(() => {
    getTagList({
      isSetNavigation: 1,
      page: 1,
      length: 999,
    }).then((res) => {
      addedList.value = res?.data?.list || [];
      reload();
    });
  });
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 4px;
  }
  :deep(.ant-pagination.mini.ant-table-pagination) {
    display: flex;
    align-items: center;
    width: 100%;
    .ant-pagination-total-text {
      flex: 1;
      height: 32px;
      line-height: 32px;
    }
  }
  :deep(.ant-checkbox-disabled::after) {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #0960bd;
    border-radius: 2px;
    visibility: hidden;
    animation: antCheckboxEffect 0.36s ease-in-out;
    animation-fill-mode: backwards;
    content: '';
  }
  :deep(.ant-checkbox-disabled .ant-checkbox-inner::after) {
    position: absolute;
    display: table;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    opacity: 1;
    transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    content: ' ';
    border-color: rgba(0, 0, 0, 0.25);
    animation-name: none;
  }
</style>
