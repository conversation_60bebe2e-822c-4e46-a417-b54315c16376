<template>
  <div class="relative pb-2 mb-4 border">
    <a-tabs v-model:activeKey="currentListTab" type="card" @change="tabChange">
      <template #tabBarExtraContent>
        <div class="extra-box">
          <div
            v-show="currentListTab === 'all'"
            class="cursor-pointer btn-search"
            @click="isSearch = !isSearch"
          >
            <search-outlined :style="{ fontSize: '20px', marginRight: '5px' }" />
            搜索
          </div>
          <div class="tip"
            >{{ isAlbum ? config[channel]['ableumTip'] : config[channel]['tip'] }}
          </div>
          <div v-if="isAlbum" class="tip"> 注：若合集中无视频，则屏幕不展示 </div>
        </div>
      </template>
      <a-tab-pane key="all" :tab="'全部(' + allCount + ')'">
        <BasicTable @register="registerTable" style="padding-top: 0px; padding-bottom: 5px" />
        <div class="recomButton">
          <a-button
            type="primary"
            block
            :disabled="!num1 || recommendAccount + num1 > 100"
            @click="handleSelectRows"
            >添加{{ num1 && `(已选${num1})` }}</a-button
          >
        </div>
      </a-tab-pane>

      <a-tab-pane key="recommend" :tab="'已推荐(' + recommendAccount + ')'">
        <BasicTable
          @register="registerRecommendTable"
          :dataSource="recommendList"
          :row-key="(record) => record.id"
          :loading="recomLoading"
          style="padding-top: 0px; padding-bottom: 5px"
        >
          <template #action="{ record, index }">
            <TableAction
              :actions="[
                {
                  icon: 'material-symbols:vertical-align-top-rounded',
                  tooltip: '置顶',
                  onClick: handleMoveTop.bind(null, record, index),
                },
                {
                  icon: 'iconoir:move-up',
                  tooltip: '上移',
                  onClick: handleMoveUp.bind(null, record, index),
                },
                {
                  icon: 'iconoir:move-down',
                  tooltip: '下移',
                  onClick: handleMoveDown.bind(null, record, index),
                },

                {
                  icon: 'uil:trash-alt',
                  color: 'error',
                  tooltip: '删除',
                  onClick: handleDel.bind(null, record),
                },
              ]"
            />
          </template>
        </BasicTable>
        <div class="recomButton">
          <a-button type="primary" block :disabled="!num2" @click="handleRemove">移除</a-button>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
  import { defineComponent, ref, watch, inject, computed, unref } from 'vue';
  import { Tabs, message } from 'ant-design-vue';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    videoColumns,
    videoPcSearch,
    videoMobileSearch,
    albumColumns,
    albumSearch,
  } from './recommendList';
  import { useContentManager } from '/@/store/modules/contentManager';

  // 接口
  import { getVideoList, getVideoListCount } from '/@/api/videoManagement/operating';
  import { getCollectionList } from '/@/api/videoManagement/collection';
  export default defineComponent({
    name: 'Recommend',
    components: {
      BasicTable,
      ATabs: Tabs,
      ATabPane: Tabs.TabPane,
      SearchOutlined,
      TableAction,
    },
    props: {
      recommendListAccount: {
        type: Number,
      },
      requstParams: {
        type: Object,
        default: () => ({}),
      },
      isImmediateLoad: {
        type: Boolean,
        default: true,
      },
    },
    emits: ['handleAccount'],
    expose: ['reset'],
    setup(props, { emit }) {
      // 当前平台类型
      const channel = inject('channel');
      const { currentType } = inject('modalVersion');
      const { currentKey: modalKey } =
        currentType.value === 'rowHalf' ? inject('twoColumnModal') : ref('');
      const contentStore = useContentManager();
      const currentListTab = ref('all');
      const isSearch = ref(false);
      const selectedRows = ref();
      const selectedRowKeys = ref();
      const selectedRows2 = ref();
      const selectedRowKeys2 = ref();
      const num1 = ref(null);
      const num2 = ref(null);
      const recomLoading = ref(false);
      const config = {
        pc: {
          tip: '仅支持已发布状态中长视频',
          ableumTip: '仅支持已发布状态合集',
        },
        mobile: {
          tip: '仅支持已发布状态视频',
          ableumTip: '仅支持已发布状态合集',
        },
      };
      // 判断类型
      const isRowHalf = computed(() => currentType.value === 'rowHalf');
      const isRowHalfLeft = computed(() => isRowHalf.value && modalKey.value === 'left');
      const isRowHalfRight = computed(() => isRowHalf.value && modalKey.value === 'right');
      const isAlbum = computed(() => currentType.value === 'album');
      // 当前加载页码数
      const currentPage = ref(1);

      //  全部视频列表
      const allCount = ref('努力加载中...');
      const [
        registerTable,
        { getSelectRows, getSelectRowKeys, clearSelectedRowKeys, reload, getForm },
      ] = useTable({
        fetchSetting: {
          pageField: 'page',
          sizeField: 'length',
        },
        beforeFetch: (params) => {
          if (isAlbum.value) {
            params['gatherType'] = 1;
            params['gatherStatus'] = [1];
            params['auditStatus'] = 2;
          } else {
            params['videoStatus'] = [1];
            if (channel === 'pc') {
              params['videoSort'] = [1];
            } else {
              params['videoSort'] = params['videoSort'] || [1, 2];
            }
          }
          // 筛选转码成功状态视频
          params['isTranscoding'] = 1;

          return { ...props.requstParams, ...params };
        },
        api: async (params) => {
          let data;
          //  全部合集数据
          if (isAlbum.value) {
            const res2 = await getCollectionList(params);
            data = res2?.data?.list.map((item) => ({
              id: item.gatherInfo.id,
              name: item.gatherInfo.name,
              price: item.price,
              videoCount: item.videoCount,
              buyCount: item.gatherInfo.buyCount,
              createAt: item.gatherInfo.createAt,
            }));
            allCount.value = res2?.data?.count || 0;
          } else {
            // 全部视频数据
            const res = await getVideoList(params);
            data = res?.data?.list.map((item) => ({
              id: item.videoInfo.id,
              name: item.videoInfo.name,
              no: item.videoInfo.no,
              typeName: item.typeName,
              tags: item.videoInfo.tags,
              payTypeStr: item.payTypeStr,
              tagInfos: item.tagInfos,
              videoInfo: item.videoInfo,
            }));
            const countRes = await getVideoListCount(params);
            allCount.value = countRes?.data?.count;
          }
          return {
            items: data ?? [],
            total: allCount.value || 0,
          };
        },
        rowKey: 'id',
        columns: isAlbum.value ? albumColumns : videoColumns,
        showIndexColumn: false,
        useSearchForm: isSearch,
        formConfig: {
          baseColProps: { span: 8 },
          labelWidth: 90,
          compact: true, //紧凑类型表单
          fieldMapToTime: [
            ['releaseTime', ['releaseTimeBegin', 'releaseTimeEnd'], 'YYYY-MM-DD HH:mm:ss'],
          ],
          schemas: isAlbum.value
            ? albumSearch
            : channel === 'pc'
            ? videoPcSearch
            : videoMobileSearch,
          showAdvancedButton: false,
          autoSubmitOnEnter: true,
          colon: true,
          actionColOptions: { span: isAlbum.value ? 8 : channel === 'pc' ? 24 : 16 },
        },
        showTableSetting: false,
        bordered: true,
        clickToRowSelect: false,
        clearSelectOnPageChange: false,
        rowSelection: computed(() => {
          return {
            type: 'checkbox',
            onChange(selectedRowKeys) {
              num1.value = selectedRowKeys?.length;
              selectedRowKeys.value = selectedRowKeys;
            },
            getCheckboxProps(record) {
              return {
                disabled: recommendList.value.findIndex((item) => item.id == record.id) > -1,
              };
            },
          };
        }),
        pagination: {
          showQuickJumper: true,
          showSizeChanger: false,
          pageSize: 10,
          current: currentPage.value,
          change(page) {
            currentPage.value = page;
          },
        },
        size: 'small',
        scroll: { y: 500, x: 1000 },
        immediate: props.isImmediateLoad,
      });

      const storeLeftList = computed(() => contentStore.videoList.get('leftVideoList') ?? []);
      const storeRightList = computed(() => contentStore.videoList.get('rightVideoList') ?? []);
      const storeAlbumList = computed(() => contentStore.albumList ?? []);
      const storeVideoList = computed(() => contentStore.videoList.get('videoList') ?? []);

      //  已推荐列表
      const recommendList = computed(() => {
        if (isRowHalf.value) {
          if (isRowHalfLeft.value) {
            return storeLeftList.value;
          } else {
            return storeRightList.value;
          }
        } else if (isAlbum.value) {
          return storeAlbumList.value;
        } else {
          return storeVideoList.value;
        }
      });

      const recommendAccount = computed(() => {
        const len = recommendList.value?.length || 0;
        emit('handleAccount', len);
        return len;
      });

      const [
        registerRecommendTable,
        { getSelectRowKeys: getSelectRowKeys2, clearSelectedRowKeys: clearSelectedRowKeys2 },
      ] = useTable({
        rowKey: 'id',
        columns: isAlbum.value ? albumColumns : videoColumns,
        showIndexColumn: false,
        showTableSetting: false,
        bordered: true,
        actionColumn: {
          width: 150,
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
        clickToRowSelect: false,
        clearSelectOnPageChange: false,
        rowSelection: {
          type: 'checkbox',
          onChange(selectedRowKeys) {
            num2.value = selectedRowKeys?.length;
          },
        },
        pagination: {
          showQuickJumper: true,
          showSizeChanger: false,
          pageSize: 10,
        },
        size: 'small',
        scroll: { y: 500, x: 1000 },
      });

      // 切换面板的回调
      async function tabChange(val) {
        if (val === 'recommend') {
          isSearch.value = false;
        } else {
          reload();
        }
      }

      //all列表-添加 按钮
      function handleSelectRows() {
        const rows = getSelectRows();
        selectedRowKeys.value = getSelectRowKeys();
        const num = recommendAccount?.value?.length + rows.length;
        if (num > 100) {
          message.warning('最多仅支持推荐100个');
        } else {
          let arr = [...unref(recommendList)];
          arr.unshift(...rows);
          arr = [...new Set(arr)];
          setRecommendList(unref(arr));
          console.log(unref(arr));
          message.success('成功添加至已推荐');
          reload();
          clearSelectedRowKeys();
        }
      }

      // 已推荐列表-数据置顶
      function handleMoveTop(record) {
        if (record?.id === recommendList?.value[0]?.id) {
          message.error('该条数据已置顶');
        } else {
          const index = recommendList.value.findIndex((it) => it.id === record.id);
          recommendList.value.unshift(recommendList.value.splice(index, 1)[0]);
          // setRecommendList();
        }
      }

      // 已推荐列表-数据上移
      function handleMoveUp(record) {
        if (record?.id === recommendList?.value[0]?.id) {
          message.error('该条数据已置顶，不可再上移');
        } else {
          const index = recommendList.value.findIndex((it) => it.id === record.id);
          const temp = recommendList.value[index - 1];
          recommendList.value[index - 1] = recommendList.value[index];
          recommendList.value[index] = temp;
          // setRecommendList();
        }
      }
      // 已推荐列表-数据下移
      function handleMoveDown(record) {
        if (record?.id === recommendList?.value[recommendAccount?.value - 1]?.id) {
          message.error('该条数据已置底，不可再下移');
        } else {
          const index = recommendList.value.findIndex((it) => it.id === record.id);
          const temp = recommendList.value[index + 1];
          recommendList.value[index + 1] = recommendList.value[index];
          recommendList.value[index] = temp;
          // setRecommendList();
        }
      }

      // 已推荐列表-数据删除
      function handleDel(record) {
        const list = recommendList.value.filter((item) => item.id !== record.id);
        setRecommendList(list);
      }

      // 已推荐列表-批量移除
      function handleRemove() {
        selectedRowKeys2.value = getSelectRowKeys2();
        selectedRowKeys2.value.forEach((item) => {
          const i = recommendList.value.findIndex((it) => it.id === item);
          if (i > -1) {
            recommendList.value.splice(i, 1);
          }
        });
        // setRecommendList();
        clearSelectedRowKeys2();
      }

      function setRecommendList(list) {
        if (isAlbum.value) {
          contentStore.setAlbumList(list);
        } else {
          if (isRowHalfLeft.value) {
            contentStore.setVideoList('leftVideoList', list);
          } else if (isRowHalfRight.value) {
            contentStore.setVideoList('rightVideoList', list);
          } else {
            contentStore.setVideoList('videoList', list);
          }
        }
      }

      function reset() {
        isSearch.value && getForm().resetFields();
        reload();
      }

      return {
        channel,
        config,
        storeAlbumList,
        storeLeftList,
        storeRightList,
        storeVideoList,
        isAlbum,
        isRowHalf,
        isRowHalfLeft,
        isRowHalfRight,
        currentType,
        currentListTab,
        isSearch,
        selectedRows,
        selectedRowKeys,
        selectedRows2,
        selectedRowKeys2,
        recommendAccount,
        allCount,
        recommendList,
        num1,
        num2,
        recomLoading,
        registerTable,
        registerRecommendTable,
        getSelectRowKeys2,
        tabChange,
        handleSelectRows,
        handleMoveTop,
        handleMoveUp,
        handleMoveDown,
        handleDel,
        handleRemove,
        reload,
        reset,
      };
    },
  });
</script>

<style lang="less" scoped>
  :deep(.ant-form) {
    padding-top: 0;
  }
  :deep(.ant-checkbox-disabled::after) {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #0960bd;
    border-radius: 2px;
    visibility: hidden;
    animation: antCheckboxEffect 0.36s ease-in-out;
    animation-fill-mode: backwards;
    content: '';
  }
  :deep(.ant-checkbox-disabled .ant-checkbox-inner::after) {
    position: absolute;
    display: table;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    opacity: 1;
    transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    content: ' ';
    border-color: rgba(0, 0, 0, 0.25);
    animation-name: none;
  }

  .btn-search {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0960bd;
  }
  :deep(.vben-basic-table-form-container) {
    padding: 0;
  }
  .recomButton {
    position: absolute;
    bottom: 5px;
    left: 8px;
    width: 200px;
  }
  .tip {
    padding: 0 10px;
    height: 28px;
    line-height: 28px;
    background-color: rgb(247, 209, 172);
    border: 1px solid rgb(226, 157, 87);
  }
  .extra-box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex: 1;
    width: 100%;
    height: 40px;

    > * {
      margin-right: 10px;
    }
  }
</style>
